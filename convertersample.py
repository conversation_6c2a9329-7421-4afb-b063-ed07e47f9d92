import os
from pathlib import Path
from marker.converters.pdf import PdfConverter
from marker.models import create_model_dict
from marker.output import text_from_rendered
from marker.config.parser import ConfigParser



def convert_pdf(input_pdf: str | os.PathLike, output_dir: str | os.PathLike, extra_config: dict | None = None):
    """Convert PDF to text, markdown, and images."""
    base_config = {
        "output_format": "markdown", 
        "strip_existing_ocr": False,
        "use_llm": False,
    }
    if extra_config:
        base_config.update(extra_config)

    parser = ConfigParser(base_config)
    converter = PdfConverter(
        config=parser.generate_config_dict(),
        artifact_dict=create_model_dict(),
        llm_service=parser.get_llm_service(),
    )

    rendered = converter(str(input_pdf))
    text, _, images = text_from_rendered(rendered)

    # 建立輸出目錄
    out_path = Path(output_dir)
    out_path.mkdir(parents=True, exist_ok=True)
    stem = Path(input_pdf).stem

    # --- Save Text ---
    (out_path / f"{stem}.txt").write_text(str(text), encoding="utf-8")

    # --- Save Markdown ---
    (out_path / f"{stem}.md").write_text(rendered.markdown, encoding="utf-8")

    # --- Save Images ---
    img_dir = out_path / f"{stem}_images"
    img_dir.mkdir(exist_ok=True)
    for idx, (name, img) in enumerate(images.items(), 1):
        img_path = img_dir / f"{idx:03d}_{name}.png"
        # If img is a PIL Image object, use the save method
        if hasattr(img, 'save'):
            img.save(img_path, format='PNG')
        else:
            # If img is already bytes data, write it directly to the file
            with open(img_path, "wb") as f:
                f.write(img)

    print(f"✓ {input_pdf} completes conversion； photos: {len(images)} ")
    return rendered
