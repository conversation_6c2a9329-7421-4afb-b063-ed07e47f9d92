
from convertersample import convert_pdf

input_file = "sample.pdf"

configs = [
    {},  # Exisiting Setting
    {"strip_existing_ocr": True},  # Re OCR
    {
        "use_llm": True,
        "llm_service": "marker.services.gemini.GoogleGeminiService",
        "gemini_api_key": "AIzaSyCe9CqQk6PzXhrLbF1XbGap-iypsT5koLs",
    },
]

for i, cfg in enumerate(configs, 1):
    convert_pdf(input_file, output_dir="outputs", extra_config=cfg)