# proxy-simple.ps1
# 簡化版 Windows 代理腳本（可直接運行）

param(
    [Parameter(Position=0)]
    [ValidateSet("on", "off", "status", "test", "help")]
    [string]$Action = "help",
    
    [switch]$Http
)

# 配置（請修改為你的憑證）
$Config = @{
    NordVPNUser = "2qfCDkqLEw8P3kf4ZAdm5zMu"
    NordVPNPass = "7R4LttY9hDK8rHjSmvBPM7q7"
    ProxyServer = "los-angeles.us.socks.nordhold.net"
    ProxyPort = "1080"
    HttpProxyPort = "8888"
}

function Write-Success($message) {
    Write-Host "✓ $message" -ForegroundColor Green
}

function Write-Error($message) {
    Write-Host "✗ $message" -ForegroundColor Red
}

function Write-Info($message) {
    Write-Host "$message" -ForegroundColor Yellow
}

switch ($Action) {
    "on" {
        if ($Http) {
            # 檢查 gost
            if (!(Get-Command gost -ErrorAction SilentlyContinue)) {
                Write-Error "需要安裝 gost 來支持 HTTP 代理"
                Write-Host "安裝方法："
                Write-Host "1. Scoop: scoop install gost"
                Write-Host "2. 手動下載: https://github.com/ginuerzh/gost/releases"
                exit 1
            }
            
            # 檢查是否已運行
            $gostProcess = Get-Process -Name "gost" -ErrorAction SilentlyContinue
            if (!$gostProcess) {
                Write-Info "正在啟動 HTTP 代理服務..."
                $gostCmd = "gost -L=http://:$($Config.HttpProxyPort) -F=socks5://$($Config.NordVPNUser):$($Config.NordVPNPass)@$($Config.ProxyServer):$($Config.ProxyPort)"
                Start-Process powershell -ArgumentList "-WindowStyle Hidden -Command `"$gostCmd`"" -PassThru | Out-Null
                Start-Sleep -Seconds 2
            }
            
            # 設置 HTTP 代理環境變量
            [Environment]::SetEnvironmentVariable("HTTP_PROXY", "http://127.0.0.1:$($Config.HttpProxyPort)", "User")
            [Environment]::SetEnvironmentVariable("HTTPS_PROXY", "http://127.0.0.1:$($Config.HttpProxyPort)", "User")
            $env:HTTP_PROXY = "http://127.0.0.1:$($Config.HttpProxyPort)"
            $env:HTTPS_PROXY = "http://127.0.0.1:$($Config.HttpProxyPort)"
            
            Write-Success "HTTP 代理已啟用 (127.0.0.1:$($Config.HttpProxyPort))"
        }
        else {
            # SOCKS5 代理
            $proxyUrl = "socks5://$($Config.NordVPNUser):$($Config.NordVPNPass)@$($Config.ProxyServer):$($Config.ProxyPort)"
            [Environment]::SetEnvironmentVariable("ALL_PROXY", $proxyUrl, "User")
            $env:ALL_PROXY = $proxyUrl
            $env:HTTP_PROXY = $proxyUrl
            $env:HTTPS_PROXY = $proxyUrl
            
            Write-Success "SOCKS5 代理已啟用 ($($Config.ProxyServer):$($Config.ProxyPort))"
            Write-Info "提示: 某些工具需要 HTTP 代理，使用 '.\proxy-simple.ps1 on -Http'"
        }
        
        # 設置 NO_PROXY
        $noProxy = "localhost,127.0.0.1,*.local,10.0.0.0/8,**********/12,***********/16"
        [Environment]::SetEnvironmentVariable("NO_PROXY", $noProxy, "User")
        $env:NO_PROXY = $noProxy
    }
    
    "off" {
        # 清除環境變量
        [Environment]::SetEnvironmentVariable("ALL_PROXY", $null, "User")
        [Environment]::SetEnvironmentVariable("HTTP_PROXY", $null, "User")
        [Environment]::SetEnvironmentVariable("HTTPS_PROXY", $null, "User")
        [Environment]::SetEnvironmentVariable("NO_PROXY", $null, "User")
        
        Remove-Item Env:ALL_PROXY -ErrorAction SilentlyContinue
        Remove-Item Env:HTTP_PROXY -ErrorAction SilentlyContinue
        Remove-Item Env:HTTPS_PROXY -ErrorAction SilentlyContinue
        Remove-Item Env:NO_PROXY -ErrorAction SilentlyContinue
        
        # 停止 gost
        Get-Process -Name "gost" -ErrorAction SilentlyContinue | Stop-Process -Force
        
        Write-Success "代理已禁用"
    }
    
    "status" {
        Write-Host "=== 代理狀態 ===" -ForegroundColor Blue
        
        if ($env:HTTP_PROXY -or $env:ALL_PROXY) {
            Write-Success "代理已啟用"
            if ($env:HTTP_PROXY) { Write-Host "HTTP_PROXY: $env:HTTP_PROXY" }
            if ($env:ALL_PROXY) { Write-Host "ALL_PROXY: $env:ALL_PROXY" }
            if ($env:NO_PROXY) { Write-Host "NO_PROXY: $env:NO_PROXY" }
        }
        else {
            Write-Info "代理未啟用"
        }
        
        $gostProcess = Get-Process -Name "gost" -ErrorAction SilentlyContinue
        if ($gostProcess) {
            Write-Success "HTTP 代理服務運行中 (PID: $($gostProcess.Id))"
        }
    }
    
    "test" {
        Write-Info "測試代理連接..."
        
        try {
            if ($env:HTTP_PROXY -like "http://*") {
                $response = Invoke-WebRequest -Uri "https://api.ipify.org" -Proxy $env:HTTP_PROXY -TimeoutSec 10
                $ip = $response.Content.Trim()
            }
            else {
                # 使用 curl 測試 SOCKS5
                $ip = & curl.exe -s --socks5-hostname "$($Config.ProxyServer):$($Config.ProxyPort)" `
                        -U "$($Config.NordVPNUser):$($Config.NordVPNPass)" `
                        https://api.ipify.org 2>$null
            }
            
            if ($ip) {
                Write-Success "代理連接成功！"
                Write-Host "你的 IP: $ip" -ForegroundColor Green
            }
            else {
                Write-Error "無法獲取 IP"
            }
        }
        catch {
            Write-Error "連接失敗: $_"
        }
    }
    
    "help" {
        Write-Host @"
NordVPN 代理管理工具 (Windows)

用法: .\proxy-simple.ps1 <命令> [-Http]

命令:
  on       - 啟用代理
  off      - 禁用代理
  status   - 查看狀態
  test     - 測試連接
  help     - 顯示幫助

選項:
  -Http    - 使用 HTTP 代理模式（需要 gost）

示例:
  .\proxy-simple.ps1 on -Http    # 啟用 HTTP 代理
  .\proxy-simple.ps1 on          # 啟用 SOCKS5 代理
  .\proxy-simple.ps1 off         # 禁用代理
  .\proxy-simple.ps1 test        # 測試連接

Git 配置:
  啟用代理後運行:
  git config --global http.proxy http://127.0.0.1:8888
  git config --global https.proxy http://127.0.0.1:8888

npm 配置:
  npm config set proxy http://127.0.0.1:8888
  npm config set https-proxy http://127.0.0.1:8888
"@ -ForegroundColor Cyan
    }
}